openapi: '3.0.0'

info:
    description: >
        Pentabell2.0 API provides endpoints for managing users, authentication, skills, clients, managers, candidates, job openings and more.
        It supports features such as user registration, sign-in, password management, and CRUD operations for various entities.
    version: '1.1.0'
    title: 'Pentabell2.0 API'
    contact:
        name: Pentabell2.0
        email: <EMAIL>
    lastUpdate: '2023-12-04'

servers:
    - url: 'http://localhost:4000/api/v1'
    - url: 'http://**************:4000/api/v1'
    - url: 'http://**************/api/v1'

components:
    securitySchemes:
        cookieAuth:
            type: apiKey
            in: cookie
            name: accessToken

    schemas:
        CreateUserRequest:
            type: object
            properties:
                firstName:
                    type: string
                lastName:
                    type: string
                email:
                    type: string
                jobTitle:
                    type: string
                country:
                    type: string
                phone:
                    type: string
                role:
                    $ref: '#/components/schemas/Role'
        CreateBlogRequest:
            type: object
            properties:
                image:
                    type: string
                visibility:
                    $ref: '#/components/schemas/Visibility'
                versions:
                    type: array
                    items:
                        $ref: '#/components/schemas/BlogVersion'
                robotsMeta:
                    $ref: '#/components/schemas/RobotsMeta'
                shareOnSocialMedia:
                    type: boolean
        CreateCategoryRequest:
            type: object
            properties:
                name:
                    type: string
                description:
                    type: string
                image:
                    type: string
                keywords:
                    type: array
        BlogVersion:
            type: object
            properties:
                language:
                    $ref: '#/components/schemas/Language'
                title:
                    type: string
                description:
                    type: string
                keywords:
                    type: array
                metaTitle:
                    type: string
                metaDescription:
                    type: string

        SignUpRequest:
            type: object
            properties:
                firstName:
                    type: string
                lastName:
                    type: string
                email:
                    type: string
                password:
                    type: string
                phone:
                    type: string
                gender:
                    $ref: '#/components/schemas/Gender'
                country:
                    type: string
                jobTitle:
                    type: string
        SignInRequest:
            type: object
            properties:
                email:
                    type: string
                password:
                    type: string
        ForgotPasswordRequest:
            type: object
            properties:
                email:
                    type: string
        ResetPasswordRequest:
            type: object
            properties:
                password:
                    type: string
        AssignRecruiterRequest:
            type: object
            properties:
                recruiters:
                    type: array
                    items:
                        type: string
        UpdateCandidateListRequest:
            type: object
            properties:
                from:
                    type: string
                to:
                    type: string
        User:
            type: object
            properties:
                firstName:
                    type: string
                lastName:
                    type: string
                email:
                    type: string
                jobTitle:
                    type: string
                country:
                    type: string
                phone:
                    type: string
                role:
                    $ref: '#/components/schemas/Role'
                profilePicture:
                    type: string
                password:
                    type: string
        Education:
            type: object
            properties:
                degree:
                    type: string
                    example: Bachelor's
                startDate:
                    type: string
                    example: '2018-09-01'
                endDate:
                    type: string
                    example: '2023-05-31'
                university:
                    type: string
                    example: University of Oxford
        Experience:
            type: object
            properties:
                title:
                    type: string
                    example: experience
                company:
                    type: string
                    example: comapny Example
                contractType:
                    type: string
                    example: Full-time
                duration:
                    type: string
                    example: 3 years
                location:
                    type: string
                    example: Tunis
                startDate:
                    type: string
                    example: '2019-08-10'
                endDate:
                    type: string
                    example: '2019-10-10'

        Certification:
            type: object
            properties:
                title:
                    type: string
                    example: Certification Title
                academy:
                    type: string
                    example: Certification Academy
                startDate:
                    type: string
                    example: '2022-01-01'
                endDate:
                    type: string
                    example: '2022-0-01'
        Candidate:
            type: object
            properties:
                certifications:
                    type: array
                    items:
                        $ref: '#/components/schemas/Certification'
                dateCreation:
                    type: string
                dateUpdate:
                    type: string
                educations:
                    type: array
                    items:
                        $ref: '#/components/schemas/Education'
                emails:
                    type: array
                    items:
                        type: string
                skills:
                    type: array
                    items:
                        type: string
                experiences:
                    type: array
                    items:
                        $ref: '#/components/schemas/Experience'
                experiencesCompany:
                    type: array
                    items:
                        type: string
                fileName:
                    type: string
                cv:
                    type: string
                summary:
                    type: string
        Client:
            type: object
            properties:
                name:
                    type: string
                hidden:
                    type: boolean
                industry:
                    type: string
                logo:
                    type: string

        Manager:
            type: object
            properties:
                furstName:
                    type: string
                lastName:
                    type: string
                country:
                    type: string
                email:
                    type: string
                phone:
                    type: string
                client:
                    type: string
        Opportunity:
            type: object
            properties:
                title:
                    type: string
                    example: 'Software Engineer'
                    required: true
                jobDescription:
                    type: string
                    example: 'we are looking for a talented Software intern to join our team.'
                    required: true
                dateOfExpiration:
                    type: string
                    example: '2024-03-05'
                dateOfRequisition:
                    type: string
                industry:
                    $ref: '#/components/schemas/Industry'
                    example: 'It & Telecom'
                    required: true
                country:
                    type: string
                    example: 'France'
                    required: true
                genre:
                    $ref: '#/components/schemas/Genre'
                    example: 'Both'
                    required: true
                createdBy:
                    type: string
                    required: true
                applicants:
                    type: array
                    items:
                        type: string
                status:
                    $ref: '#/components/schemas/JobStatus'
                    default: Open
                isPublished:
                    type: boolean
                    default: false
        Application:
            type: object
            properties:
                # status:
                #   $ref: '#/components/schemas/applicationStatus'
                note:
                    type: string
                    example: "I'm excited to join your team."
                    required: true
                # candidate:
                #   type: string
                opportunity:
                    type: string
        updateApplication:
            type: object
            properties:
                status:
                    $ref: '#/components/schemas/applicationStatus'
        Alert:
            type: object
            properties:
                createdBy:
                    type: string
                industry:
                    type: array
                    items:
                        type: string
                country:
                    type: array
                    items:
                        type: string
                isActive:
                    type: string

        Opening:
            type: object
            properties:
                project:
                    type: object
                    properties:
                        opportunityType:
                            type: string
                        dateOfRequisition:
                            type: string
                        deadline:
                            type: string
                        client:
                            $ref: '#/components/schemas/Client'
                        clientManager:
                            $ref: '#/components/schemas/Manager'
                        businessManager:
                            $ref: '#/components/schemas/User'
                        name:
                            type: string
                        country:
                            type: string
                        region:
                            type: string
                        jobTitle:
                            type: string
                        startDate:
                            type: string
                        duration:
                            type: string
                        contractType:
                            type: string
                        contractForm:
                            type: string
                        numberOfConsultants:
                            type: number
                        industry:
                            type: string
                compensation:
                    type: object
                    properties:
                        hiringStatus:
                            type: string
                        workingHoursPerDay:
                            type: number
                        workingDaysPerWeek:
                            type: number
                        salary:
                            type: object
                            properties:
                                rate:
                                    type: number
                                currency:
                                    type: string
                                type:
                                    type: string
                                per:
                                    type: string
                        leaveEntitlementPerYear:
                            type: number
                        airTicketPerYear:
                            type: number
                        rotationIfAny:
                            type: string
                        dailyAllowance:
                            type: number
                        housing:
                            type: boolean
                        localTransportation:
                            type: boolean
        Skill:
            type: object
            properties:
                name:
                    type: string
                industry:
                    $ref: '#/components/schemas/Industry'
        SearchHistory:
            type: object
            properties:
                query:
                    type: object
                    properties:
                        jobTitleAng:
                            type: string
                        skills:
                            type: array
                            items:
                                type: string
                        locations:
                            type: array
                            items:
                                type: string
                        industry:
                            $ref: '#/components/schemas/Industry'
                        industries:
                            type: array
                            items:
                                type: string

        Role:
            type: string
            enum:
                - Admin
                - Recruit
                - Recruit manager
                - Commercial
                - Commercial manager
        Visibility:
            type: string
            enum:
                - Public
                - Private
                - Draft
        Language:
            type: string
            enum:
                - fr
                - en
        RobotsMeta:
            type: string
            enum:
                - index
                - noindex
        Industry:
            type: string
            enum:
                - It & Telecom
                - Transport
                - Oil & gas
                - Energy
                - Banking
                - Others
        applicationStatus:
            type: string
            enum:
                - Pending
                - Accepted
                - Rejected
        Genre:
            type: string
            enum:
                - Female
                - Male
                - Both
        JobStatus:
            type: string
            enum:
                - Open
                - Closed
        candidateStatus:
            type: string
            enum:
                - New
                - Active
                - Inactive
                - Blacklisted
        Posting:
            type: string
            enum:
                - Confidential
                - Publish
        OpeningState:
            type: string
            enum:
                - New
                - Open
                - Deal
                - Interview
                - Rejected
                - Closed
        OpportunityType:
            type: string
            enum:
                - Consulting
                - Direct Hire
                - Tender
                - Capability
                - Payroll
                - Intern
        HiringStatus:
            type: string
            enum:
                - Single
                - Married
        CurrencyType:
            type: string
            enum:
                - Net
                - Gross
        SalaryCurrency:
            type: string
            enum:
                - Euro
                - Dollar
        SalaryPer:
            type: string
            enum:
                - Daily
                - Monthly
                - Annual
        Experiences:
            type: string
            enum:
                - 0-2
                - 2-5
                - 5-10
                - 10-15
                - '+15'
        Languages:
            type: string
            enum:
                - ENGLISH
                - FRENCH
                - ARABIC
        ContractForm:
            type: string
            enum:
                - Local
                - International
                - Local International
        ContractType:
            type: string
            enum:
                - CDD
                - CDI
                - CDIC
                - CDM
        UserStatus:
            type: string
            enum:
                - Blocked
                - Archived
                - Confirmed
                - Not Confirmed
        Gender:
            type: string
            enum:
                - Men
                - Women
paths:
    /auth/signup:
        post:
            summary: Register a new user
            tags:
                - Auth
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SignUpRequest'
                        examples:
                            example1:
                                summary: Example of a candidate registration
                                value:
                                    firstName: candidate 0
                                    lastName: candidate 0
                                    email: <EMAIL>
                                    password: test123456
                                    phone: '+21628523641'
                                    country: Tunisia

            responses:
                '201':
                    description: User registered successfully
                '400':
                    description: Bad request
    /auth/signin:
        post:
            summary: Sign in as a user
            tags:
                - Auth
            requestBody:
                description: User sign-in data
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SignInRequest'
                        examples:
                            example1:
                                summary: Example of candidate login
                                value:
                                    email: <EMAIL>
                                    password: test123456

                            example2:
                                summary: Example of admin login
                                value:
                                    email: <EMAIL>
                                    password: admin123456
            responses:
                '200':
                    description: User signed in successfully
                '401':
                    description: Unauthorized
    /auth/logout:
        post:
            summary: Log out
            tags:
                - Auth
            responses:
                '200':
                    description: User logged out successfully
    /auth/forgot-password:
        post:
            summary: Request password reset
            tags:
                - Auth
            requestBody:
                description: User email for password reset
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ForgotPasswordRequest'
            responses:
                '200':
                    description: Password reset request successful
    /auth/reset-password/{token}:
        post:
            summary: Reset password
            tags:
                - Auth
            parameters:
                - name: token
                  in: path
                  required: true
                  description: Reset password token
                  schema:
                      type: string
                - name: body
                  in: body
                  description: New password
                  required: true
                  content:
                      application/json:
                          schema:
                              $ref: '#/components/schemas/ResetPasswordRequest'
            responses:
                '200':
                    description: Password reset successful
    /auth/confirm-account/{token}:
        post:
            summary: Confirm account
            tags:
                - Auth
            parameters:
                - name: token
                  in: path
                  required: true
                  description: Confirmation token
                  schema:
                      type: string
            responses:
                '200':
                    description: Account confirmation successful
    /users:
        get:
            summary: Get all users
            tags:
                - Users
            security:
                - cookieAuth: []
            parameters:
                - in: query
                  name: role
                  schema:
                      type: string
                - in: query
                  name: status
                  schema:
                      type: string
                - in: query
                  name: firstName
                  schema:
                      type: string
                - in: query
                  name: jobTitle
                  schema:
                      type: string
                - in: query
                  name: country
                  schema:
                      type: string
                - in: query
                  name: isArchived
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
        post:
            summary: Create a new user
            tags:
                - Users
            security:
                - cookieAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateUserRequest'
                        examples:
                            example1:
                                summary: Example of creating a candidate
                                value:
                                    firstName: Candidate
                                    lastName: user
                                    email: <EMAIL>
                                    phone: +21628523641
                                    country: Tunisia
                                    roles:
                                        - Candidate
                            example4:
                                summary: Example of creating an admin
                                value:
                                    firstName: Admin
                                    lastName: User
                                    email: <EMAIL>
                                    password: admin123
                                    phone: +1234567890
                                    country: Tunisia
                                    roles:
                                        - Admin
                            example5:
                                summary: Example of creating a recruiter
                                value:
                                    firstName: Recruiter
                                    lastName: User
                                    email: <EMAIL>
                                    password: recruiter123
                                    phone: +1234567890
                                    country: Tunisia
                                    roles:
                                        - Recruiter
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '409':
                    description: Email must be unique
    /users/{id}:
        get:
            summary: Get user by ID
            tags:
                - Users
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: User not found
                '406':
                    description: Invalid Mongo ID
        put:
            summary: Update user
            tags:
                - Users
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateUserRequest'
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: User not found
        delete:
            summary: Delete user
            tags:
                - Users
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: User not found
    /users/current:
        get:
            summary: Get current User
            tags:
                - Users
            security:
                - cookieAuth: []
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
    /account:
        get:
            summary: Get account
            tags:
                - Account
            security:
                - cookieAuth: []
            parameters:
                - in: cookie
                  name: accessToken
                  required: true
            responses:
            '200':
                description: Successful response
            '401':
                description: Access denied
            '404':
                description: User not found
        put:
            summary: Update account
            tags:
                - Account
            security:
                - cookieAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        example: |
                            {
                              "firstName": "",
                              "lastName": "",
                              "phone": "",
                              "jobTitle": "",
                              "country": ""
                            }
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '404':
                    description: User not found
    /account/password:
        put:
            summary: Update password
            tags:
                - Account
            security:
                - cookieAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        example: |
                            {
                              "currentPassword": "",
                              "newPassword": ""
                            }
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '404':
                    description: User not found
    /clients:
        get:
            summary: Get clients
            tags:
                - Clients
            security:
                - cookieAuth: []
            parameters:
                - in: query
                  name: name
                  schema:
                      type: string
                - in: query
                  name: pageNumber
                  schema:
                      type: string
                - in: query
                  name: pageSize
                  schema:
                      type: string
                - in: query
                  name: hidden
                  schema:
                      type: boolean
                - in: query
                  name: paginated
                  schema:
                      type: boolean
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
        post:
            summary: Create client
            tags:
                - Clients
            security:
                - cookieAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Client'
                        examples:
                            example1:
                                summary: create client
                                value:
                                    name: 'HUAWEI'
                                    hidden: false
                                    industry: 'Transport'
                                    logo: ''
                            example2:
                                summary: Example of creating a client
                                value:
                                    name: HUAWEI
                                    hidden: false
                                    industry: 'Oil & Gas'
                                    logo: ''
                            example3:
                                summary: creating another client
                                value:
                                    name: 'Total Energie'
                                    hidden: true
                                    industry: 'It & Telecom'
                                    logo: ''
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
    /clients/{id}:
        get:
            summary: Get client by ID
            tags:
                - Clients
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '404':
                    description: Client not found
                '406':
                    description: Invalid Mongo ID
        put:
            summary: Update Client
            tags:
                - Clients
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Client'
                        examples:
                            example1:
                                summary: Example of creating a client
                                value:
                                    name: HUAWEI
                                    hidden: false
                                    industry: 'Oil & Gas'
                                    logo: ''
                            example2:
                                summary: creating another client
                                value:
                                    name: 'Total Energie'
                                    hidden: true
                                    industry: 'It & Telecom'
                                    logo: ''
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Client not found
                '406':
                    description: Invalid Mongo ID
        delete:
            summary: Delete Client
            tags:
                - Clients
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Client not found
                '406':
                    description: Invalid Mongo ID
    /clients/{id}/managers:
        get:
            summary: Get Client's Manager
            tags:
                - Clients
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Client not found
                '406':
                    description: Invalid Mongo ID
    /category:
        post:
            summary: Create a new category
            tags:
                - Articles
            security:
                - cookieAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateCategoryRequest'
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '409':
                    description: url must be unique
        get:
            summary: Get Category
            tags:
                - Articles
            security:
                - cookieAuth: []
            parameters:
                - in: query
                  name: pageNumber
                  schema:
                      type: string
                - in: query
                  name: status
                  schema:
                      type: pageSize
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
    /category/{id}:
        put:
            summary: Update Category
            tags:
                - Articles

            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateCategoryRequest'
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Category not found
                '406':
                    description: Invalid Mongo ID
    /articles:
        post:
            summary: Create a new article
            tags:
                - Articles
            security:
                - cookieAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateBlogRequest'
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '409':
                    description: url must be unique
    /articles/:
        get:
            summary: Get Articles
            tags:
                - Articles
            security:
                - cookieAuth: []
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
    /{language}/{url}:
        get:
            summary: Get Articles
            tags:
                - Articles
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: language
                  schema:
                      $ref: '#/components/schemas/Language'
                - in: path
                  name: url
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
    /articles/{language}/blog/{url}:
        get:
            summary: Get Article by url and language (without redirection)
            tags:
                - Articles
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: language
                  schema:
                      $ref: '#/components/schemas/Language'
                - in: path
                  name: url
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden

    /articles/{id}:
        put:
            summary: Update Article
            tags:
                - Articles

            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateBlogRequest'
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Manager not found
                '406':
                    description: Invalid Mongo ID
        delete:
            summary: Delete Article
            tags:
                - Articles
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Article not found
                '406':
                    description: Invalid Mongo ID
        /managers:
        get:
            summary: Get Managers
            tags:
                - Managers
            security:
                - cookieAuth: []
            parameters:
                - in: query
                  name: pageNumber
                  schema:
                      type: string
                - in: query
                  name: pageSize
                  schema:
                      type: string
                - in: query
                  name: firstName
                  schema:
                      type: string
                  description: Filter by manager's first name
                - in: query
                  name: lastName
                  schema:
                      type: string
                  description: Filter by manager's last name
                - in: query
                  name: clientName
                  schema:
                      type: string
                  description: Filter by client's name
                - in: query
                  name: paginated
                  schema:
                      type: boolean
                  description: Specify whether to paginate the results
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
        post:
            summary: Create Manager
            tags:
                - Managers
            security:
                - cookieAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Manager'
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
    /managers/{id}:
        get:
            summary: Get Manager by ID
            tags:
                - Managers
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Manager not found
                '406':
                    description: Invalid Mongo ID
        put:
            summary: Update Manager
            tags:
                - Managers
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Manager'
                        examples:
                            example1:
                                summary: Example of creating a client Manager
                                value:
                                    firstName: client
                                    lastName: Manager HUAWEI
                                    address: 'test'
                                    email: '<EMAIL>'
                                    phone: '+21625634125'
                                    client: ''
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Manager not found
                '406':
                    description: Invalid Mongo ID
        delete:
            summary: Delete Manager
            tags:
                - Managers
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Manager not found
                '406':
                    description: Invalid Mongo ID
    /opportunity:
        post:
            summary: Create Opportunity
            tags:
                - Opportunities
            security:
                - cookieAuth: []
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Opportunity'
            responses:
                '201':
                    description: Created
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Manager not found
                '406':
                    description: Invalid Mongo ID
        get:
            summary: Get Opportunities
            tags:
                - Opportunities
            security:
                - cookieAuth: []
            parameters:
                - in: query
                  name: pageNumber
                  schema:
                      type: number
                - in: query
                  name: pageSize
                  schema:
                      type: number
                - in: query
                  name: keyWord
                  description: 'title, jobDescription'
                  schema:
                      type: string
                - in: query
                  name: industry
                  schema:
                      type: string
                - in: query
                  name: country
                  schema:
                      type: string
                - in: query
                  name: createdAt
                  description: 'Date Format (YYYY-MM-dd) IE: 2024-02-26'
                  schema:
                      type: string
                - in: query
                  name: sortOrder
                  schema:
                      enum: [asc, desc]
                - in: query
                  name: isPublished
                  schema:
                      type: boolean

            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
    /opportunity/{id}:
        get:
            summary: Get Opportunity by ID
            tags:
                - Opportunities
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Opportunity not found
                '406':
                    description: Invalid Mongo ID
        put:
            summary: Update Opportunity
            tags:
                - Opportunities
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Opportunity'

            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Opportunity not found
                '406':
                    description: Invalid Mongo ID
        delete:
            summary: Delete Opportunity
            tags:
                - Opportunities
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Opportunity not found
                '406':
                    description: Invalid Mongo ID
    ########### Application #############
    /applications:
        get:
            summary: Get Applications
            tags:
                - Applications
            security:
                - cookieAuth: []
            parameters:
                - in: query
                  name: isActive
                  schema:
                      type: boolean

                - in: query
                  name: pageNumber
                  schema:
                      type: number
                - in: query
                  name: pageSize
                  schema:
                      type: number
                - in: query
                  name: paginated
                  schema:
                      type: boolean
                - in: query
                  name: jobTitle
                  schema:
                      type: string
                - in: query
                  name: opportunity
                  schema:
                      type: string
                - in: query
                  name: candidate
                  schema:
                      type: string
                - in: query
                  name: industry
                  schema:
                      $ref: '#/components/schemas/Industry'
                - in: query
                  name: status
                  schema:
                      $ref: '#/components/schemas/applicationStatus'
                - in: query
                  name: createdAt
                  description: 'Date Format (YYYY-MM-dd) IE: 2024-02-26'
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
    /applications/{id}/:
        get:
            summary: Get Application by ID
            tags:
                - Applications
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Application not found
                '406':
                    description: Invalid Mongo ID
        put:
            summary: Update Application (either accept or refuse an application).
            tags:
                - Applications
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string

            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/updateApplication'
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Application not found
                '406':
                    description: Invalid Mongo ID
        delete:
            summary: Cancel Application
            tags:
                - Applications
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Application not found
                '406':
                    description: Invalid Mongo ID
    /applications/opportunity/{id}/:
        post:
            summary: Apply for Opportunity
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            tags:
                - Applications
            security:
                - cookieAuth: []
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Application'
            responses:
                '201':
                    description: Created
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Manager not found
                '406':
                    description: Invalid Mongo ID

    /skills:
        get:
            summary: Get Skills
            tags:
                - Skills
            security:
                - cookieAuth: []
            parameters:
                - in: query
                  name: name
                  schema:
                      type: string
                - in: query
                  name: paginated
                  schema:
                      type: boolean
                - in: query
                  name: isTopTen
                  schema:
                      type: boolean
                - in: query
                  name: pageNumber
                  schema:
                      type: number
                - in: query
                  name: pageSize
                  schema:
                      type: number
                - in: query
                  name: industry
                  schema:
                      type: string
                - in: query
                  name: industries
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
        post:
            summary: Create Skill
            tags:
                - Skills
            security:
                - cookieAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Skill'
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
    /skills/import:
        post:
            summary: Create Many Skills
            tags:
                - Skills
            security:
                - cookieAuth: []
            requestBody:
                required: true
                content:
                    multipart/form-data:
                        schema:
                            type: object
                            properties:
                                file:
                                    type: string
                                    format: binary
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '406':
                    description: Not Acceptable - Invalid input data
                '409':
                    description: Skill already exists
    /skills/{id}:
        put:
            summary: Update Skill
            tags:
                - Skills
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Manager'
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Skill not found
                '406':
                    description: Not Acceptable - Invalid input data
        delete:
            summary: Delete Skill
            tags:
                - Skills
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access Forbidden
                '404':
                    description: Skill not found
    /searchHistory:
        get:
            summary: Get Search Histories
            tags:
                - Search History
            security:
                - cookieAuth: []
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
    /searchHistory/{id}:
        get:
            summary: Get Search History
            tags:
                - Search History
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '404':
                    description: Search history not found
                '406':
                    description: Invalid Mongo ID
        delete:
            summary: Delete Search History
            tags:
                - Search History
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '404':
                    description: Search history not found
                '406':
                    description: Invalid Mongo ID
    /candidate/favourite:
        get:
            summary: Get Candidate's favourite opportunities (isPublished =>8 true)
            tags:
                - Favourite Opportunities
            security:
                - cookieAuth: []
            parameters:
                - in: query
                  name: paginated
                  schema:
                      type: boolean
                - in: query
                  name: pageNumber
                  schema:
                      type: number
                - in: query
                  name: pageSize
                  schema:
                      type: number
                - in: query
                  name: industry
                  schema:
                      type: string
                - in: query
                  name: country
                  schema:
                      type: string
                - in: query
                  name: keyWord
                  description: 'title, jobDescription'
                  schema:
                      type: string
                - in: query
                  name: sortOrder
                  schema:
                      enum: [asc, desc]
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
    /candidate/favourite/{id}:
        delete:
            summary: Remove Opportunity From Candidate Short List
            tags:
                - Favourite Opportunities
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '404':
                    description: Favourite not found
                '406':
                    description: Invalid Mongo ID
        put:
            summary: Add Opportunity To Candidate Short List
            tags:
                - Favourite Opportunities
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '404':
                    description: Favourite not found
                '406':
                    description: Invalid Mongo ID

    /users/favourite:
        get:
            summary: Get Recruiter's favourite candidates
            tags:
                - Favourite Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: query
                  name: paginated
                  schema:
                      type: boolean
                - in: query
                  name: pageNumber
                  schema:
                      type: number
                - in: query
                  name: pageSize
                  schema:
                      type: number
                - in: query
                  name: industry
                  schema:
                      type: string
                - in: query
                  name: keyWord
                  description: 'title, jobDescription'
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
    /users/favourite/{id}:
        delete:
            summary: Remove Candidate From Recruiter's Short List
            tags:
                - Favourite Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '404':
                    description: Favourite not found
                '406':
                    description: Invalid Mongo ID
        put:
            summary: Add Candidate To Recruiter's Short List
            tags:
                - Favourite Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '404':
                    description: Favourite not found
                '406':
                    description: Invalid Mongo ID

    /candidats:
        get:
            summary: Get Candidates
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: query
                  name: pageNumber
                  schema:
                      type: number
                - in: query
                  name: pageSize
                  schema:
                      type: number
                - in: query
                  name: keyWord
                  description: 'jobTitle, location, skills...'
                  schema:
                      type: string
                - in: query
                  name: jobTitleAng
                  schema:
                      type: string
                - in: query
                  name: skills
                  schema:
                      type: string
                - in: query
                  name: locations
                  schema:
                      type: string
                - in: query
                  name: industry
                  schema:
                      type: string
                - in: query
                  name: industries
                  schema:
                      type: string
                - in: query
                  name: monthsOfExperiencesMin
                  schema:
                      type: number
                - in: query
                  name: monthsOfExperiencesMax
                  schema:
                      type: number
                - in: query
                  name: isValidXp
                  schema:
                      type: boolean
                - in: query
                  name: isPentabellMember
                  schema:
                      type: boolean
                - in: query
                  name: dateCreation
                  description: 'Date Format (YYYY-MM-dd) IE: 2023-11-30'
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
    /candidats/{id}:
        post:
            summary: Create Candidate
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Candidate'
                        examples:
                            example1:
                                summary: Example of adding a candidate
                                value:
                                    jobTitleAng: Software Engineer
                                    industry: It & Telecom
                                    skills:
                                        - JavaScript
                                        - React
                                    cv: cv
                                    summary: this is my summary
                                    educations:
                                        - degree: Bachelor's Degree
                                          startDate: '2015-09-01'
                                          endDate: '2019-06-01'
                                          university: Example University
                                    experiences:
                                        - title: Software Developer
                                          startDate: '2019-07-01'
                                          endDate: '2022-08-01'
                                          company: Example Company
                                    certifications:
                                        - title: Certification Title
                                          startDate: '2022-01-01'
                                          endDate: '2022-06-01'
                                          academy: Certification Academy
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '406':
                    description: Not Acceptable - Invalid input data

        get:
            summary: Get Candidate
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate not found
                '406':
                    description: Not Acceptable - Invalid Mongo ID

        put:
            summary: Update Candidate
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Candidate'
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate not found
                '406':
                    description: Not Acceptable - Invalid Mongo ID / Input Data

        delete:
            summary: Delete Candidate
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate not found
                '406':
                    description: Not Acceptable - Invalid Mongo ID
    /candidats/{id}/similar:
        get:
            summary: Get Similar Candidates
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
                - in: query
                  name: pageNumber
                  schema:
                      type: number
                - in: query
                  name: pageSize
                  schema:
                      type: number

            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate not found
                '406':
                    description: Not Acceptable - Invalid Mongo ID / Input Data
    /candidats/settings:
        get:
            summary: Get Candidates (For settings)
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: query
                  name: pageNumber
                  schema:
                      type: number
                - in: query
                  name: pageSize
                  schema:
                      type: number
                - in: query
                  name: industry
                  schema:
                      type: string
                - in: query
                  name: industries
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
    /candidats/{id}/experiences/:
        post:
            summary: Add Experience to a Candidate
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Experience'
                        examples:
                            example1:
                                summary: Example of adding an experience
                                value:
                                    title: test1
                                    company: Example Company
                                    contractType: Full-time
                                    duration: 3 months
                                    location: Example Location
                                    startDate: '2018-05-05'
                                    endDate: '2018-08-05'
            responses:
                '201':
                    description: Created - New experience added
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate not found
                '406':
                    description: Not Acceptable - Invalid Input Data

    /candidats/{id}/experiences/{experienceId}:
        put:
            summary: Update Candidate's Experience
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
                - in: path
                  name: experienceId
                  required: true
                  schema:
                      type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Experience'
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate/Experience not found
                '406':
                    description: Not Acceptable - Invalid Mongo ID
        delete:
            summary: Delete Candidate's Experience
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
                - in: path
                  name: experienceId
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate/Experience not found
                '406':
                    description: Not Acceptable - Invalid Mongo ID
    /candidats/{id}/educations/:
        post:
            summary: Add Education to a Candidate
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Education'
            responses:
                '201':
                    description: Created - New education added
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate not found
                '406':
                    description: Not Acceptable - Invalid Mongo ID
    /candidats/{id}/educations/{educationId}:
        put:
            summary: Update Candidate's Education
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
                - in: path
                  name: educationId
                  required: true
                  schema:
                      type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Education'
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate/Experience not found
                '406':
                    description: Not Acceptable - Invalid Mongo ID
        delete:
            summary: Delete Candidate's Education
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
                - in: path
                  name: educationId
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate/Experience not found
                '406':
                    description: Not Acceptable - Invalid Mongo ID

    /candidats/{id}/certifications:
        post:
            summary: Add New Certifications to Candidate
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Certification'
            responses:
                '201':
                    description: Created - New certification added
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate not found
                '406':
                    description: Not Acceptable - Invalid Input Data
    /candidats/{id}/certifications/{certificationId}:
        put:
            summary: Update Candidate's Certification
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
                - in: path
                  name: certificationId
                  required: true
                  schema:
                      type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Certification'
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate/Experience not found
                '406':
                    description: Not Acceptable - Invalid Mongo ID / Input Data
        delete:
            summary: Delete Candidate's certification
            tags:
                - Candidates
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
                - in: path
                  name: certificationId
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate/certification not found
                '406':
                    description: Not Acceptable - Invalid Mongo ID
    /alert:
        get:
            summary: Get Alerts
            tags:
                - Alerts
            security:
                - cookieAuth: []
            parameters:
                - in: query
                  name: pageNumber
                  schema:
                      type: number
                - in: query
                  name: pageSize
                  schema:
                      type: number
                - in: query
                  name: paginated
                  schema:
                      type: boolean
                - in: query
                  name: industry
                  schema:
                      type: string
                - in: query
                  name: country
                  schema:
                      type: string
                - in: query
                  name: sortOrder
                  schema:
                      enum: [asc, desc]
                - in: query
                  name: createdBy
                  schema:
                      type: string
                - in: query
                  name: isActive
                  schema:
                      type: boolean
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
        post:
            summary: create Alert
            tags:
                - Alerts
            security:
                - cookieAuth: []
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Alert'
                        examples:
                            example1:
                                summary: Example of alert
                                value:
                                    industry: ['BANKING']
                                    country: ['Canada']

            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '406':
                    description: Not Acceptable - Invalid input data

    /alert/{id}:
        get:
            summary: Get Alert
            tags:
                - Alerts
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate not found
                '406':
                    description: Not Acceptable - Invalid Mongo ID

    /alert/{id}/enable:
        put:
            summary: Enable alert
            tags:
                - Alerts
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string

            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate not found
                '406':
                    description: Not Acceptable - Invalid Mongo ID / Input Data
    /alert/{id}/disable:
        put:
            summary: Disable alert
            tags:
                - Alerts
            security:
                - cookieAuth: []
            parameters:
                - in: path
                  name: id
                  required: true
                  schema:
                      type: string

            responses:
                '200':
                    description: Successful response
                '401':
                    description: Access denied
                '403':
                    description: Access forbidden
                '404':
                    description: Candidate not found
                '406':
                    description: Not Acceptable - Invalid Mongo ID / Input Data
    /categories:
        post:
            summary: Create a new category with versions
            tags:
                - Categories
            security:
                - bearerAuth: []
            requestBody:
                required: true
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - versions
                            properties:
                                versions:
                                    type: object
                                    description: Map of language codes to category version objects
                                    additionalProperties:
                                        type: object
                                        required:
                                            - name
                                        properties:
                                            language:
                                                type: string
                                                example: en
                                            name:
                                                type: string
                                                example: Electronics
                                            url:
                                                type: string
                                                example: electronics
                                            description:
                                                type: string
                                                example: Category for electronic devices
                                            image:
                                                type: string
                                                nullable: true
                                                example: https://example.com/image.jpg
                                            canonical:
                                                type: string
                                                example: https://example.com/electronics
                                            articles:
                                                type: array
                                                items:
                                                    type: string
                                                    format: objectId
                                            guides:
                                                type: array
                                                items:
                                                    type: string
                                                    format: objectId
                                            metaTitle:
                                                type: string
                                                example: Best Electronics
                                            metaDescription:
                                                type: string
                                                example: Find the best electronics here.
                                robotsMeta:
                                    type: string
                                    enum: [index, noindex, follow, nofollow] # adjust as per your robotsMeta enum
                                    example: index

            responses:
                '200':
                    description: Category created successfully
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    category:
                                        $ref: '#/components/schemas/Category'
                '409':
                    description: Category with this name already exists
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Error'
                '500':
                    description: Internal server error
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Error'

        put:
            summary: Import a JSON file to convert to the new category model
            tags:
                - Categories
            consumes:
                - multipart/form-data
            requestBody:
                required: true
                content:
                    multipart/form-data:
                        schema:
                            type: object
                            properties:
                                file:
                                    type: string
                                    format: binary
                                    description: JSON file to upload
            responses:
                201:
                    description: Categories converted successfully
                400:
                    description: No file uploaded
                500:
                    description: Conversion failed
        get:
            tags:
                - Categories
            summary: Retrieve all categories
            description: |
                Fetches a paginated and filterable list of all categories. 
                You can use query parameters for pagination, sorting, and searching.
            parameters:
                - in: query
                  name: page
                  schema:
                      type: integer
                      default: 1
                  description: The page number for pagination.
                - in: query
                  name: limit
                  schema:
                      type: integer
                      default: 10
                  description: The number of categories to return per page.
                - in: query
                  name: sort
                  schema:
                      type: string
                  description: "Sort order. Use field name for ascending (e.g., 'createdAt') or prefix with '-' for descending (e.g., '-createdAt')."
                - in: query
                  name: search
                  schema:
                      type: string
                  description: A search term to filter categories by name or other text fields.
            responses:
                '200':
                    description: A list of categories was successfully retrieved.
                    content:
                        application/json:
                            schema:
                                type: array
                                items:
                                    $ref: '#/components/schemas/Category'
                '500':
                    description: Internal Server Error.
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Error'

        components:
            schemas:
                Category:
                    type: object
                    properties:
                        _id:
                            type: string
                            description: The unique identifier for the category.
                            example: '6707ab921e6de2d54bc907e9'
                        versions:
                            type: object
                            description: An object containing language-specific versions of the category.
                            properties:
                                en:
                                    type: object
                                    properties:
                                        name:
                                            type: string
                                            example: 'Technology'
                                        articles:
                                            type: array
                                            items:
                                                type: string
                                                example: '673208228603effaeaac6a9e'
                        createdAt:
                            type: string
                            format: date-time
                            description: The date and time the category was created.
                        updatedAt:
                            type: string
                            format: date-time
                            description: The date and time the category was last updated.

                Error:
                    type: object
                    properties:
                        status:
                            type: integer
                            example: 500
                        message:
                            type: string
                            example: 'An internal server error occurred.'
