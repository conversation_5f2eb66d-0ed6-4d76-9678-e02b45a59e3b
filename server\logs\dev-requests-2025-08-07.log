[08:25:22] - [Guest User] - [::1] - [POST] - [/api/v1/auth/signin] - [200]
[08:30:55] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[08:30:55] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[08:30:55] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [200]
[08:30:55] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[08:30:55] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[08:31:19] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[08:31:19] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[08:31:19] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[08:31:19] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [304]
[08:31:19] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[08:31:19] - [Guest User] - [::1] - [GET] - [/api-docs/favicon-32x32.png] - [304]
[10:10:53] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY40ntP] - [404]
[10:10:58] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY40p6Y] - [404]
[10:11:03] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY40qLG] - [404]
[10:11:08] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY40raJ] - [404]
[10:11:13] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY40sp6] - [404]
[10:11:18] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY40u1r] - [404]
[10:11:23] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY40vGs] - [404]
[10:11:28] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY40wVf] - [404]
[10:11:33] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY40xkE] - [404]
[10:11:38] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY40yz7] - [404]
[10:11:44] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY40-C4] - [404]
[10:11:49] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY40_Qm] - [404]
[10:11:54] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY410hz] - [404]
[10:12:00] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY4129g] - [404]
[10:12:06] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY413dW] - [404]
[10:12:12] - [Guest User] - [::1] - [GET] - [/socket.io/?EIO=4&transport=polling&t=PY4155F] - [404]
[10:12:14] - [Guest User] - [::1] - [GET] - [/api/v1/candidats/count] - [404]
[10:12:21] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[10:12:21] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[10:12:21] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[10:12:21] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [304]
[10:12:21] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[10:15:17] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[10:15:17] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[10:15:17] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [200]
[10:15:17] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[10:15:17] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[10:15:51] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[10:15:51] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[10:15:51] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [304]
[10:15:51] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[10:15:51] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[10:15:51] - [Guest User] - [::1] - [GET] - [/api-docs/favicon-32x32.png] - [304]
[10:16:12] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[10:16:12] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[10:16:12] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [304]
[10:16:12] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[10:16:12] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[10:16:23] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[10:16:23] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[10:16:23] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [304]
[10:16:23] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[10:16:23] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[10:17:02] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[10:17:02] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[10:17:02] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[10:17:02] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [304]
[10:17:02] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[10:17:10] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[10:17:10] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[10:17:10] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[10:17:10] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [200]
[10:17:10] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[10:17:10] - [Guest User] - [::1] - [GET] - [/api-docs/favicon-32x32.png] - [304]
[10:24:01] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[10:24:01] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[10:24:01] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [200]
[10:24:01] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[10:24:01] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[10:25:12] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[10:25:12] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[10:25:12] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[10:25:12] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [200]
[10:25:12] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[10:25:16] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[10:25:16] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[10:25:16] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [304]
[10:25:16] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[10:25:16] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[10:25:16] - [Guest User] - [::1] - [GET] - [/api-docs/favicon-32x32.png] - [304]
[10:25:16] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[10:25:16] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[10:25:16] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [304]
[10:25:16] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[10:25:16] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[10:25:17] - [Guest User] - [::1] - [GET] - [/api-docs/favicon-32x32.png] - [304]
[10:33:05] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[10:33:05] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[10:33:05] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[10:33:05] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [304]
[10:33:05] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[10:33:10] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[10:33:10] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[10:33:10] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[10:33:10] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [200]
[10:33:10] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[10:33:10] - [Guest User] - [::1] - [GET] - [/api-docs/favicon-32x32.png] - [304]
[10:33:49] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [404]
[10:39:41] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [400]
[10:41:45] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [400]
[10:42:51] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[10:42:51] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[10:42:51] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [200]
[10:42:51] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[10:42:51] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[10:42:51] - [Guest User] - [::1] - [GET] - [/api-docs/favicon-32x32.png] - [304]
[10:42:58] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [400]
[10:44:44] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [400]
[10:45:23] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [400]
[10:45:46] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [400]
[10:47:47] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [500]
[10:48:54] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [500]
[10:59:12] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [500]
[11:02:35] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [201]
[11:04:35] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [201]
[11:05:45] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [500]
[12:22:54] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [500]
[12:35:34] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [500]
[12:37:08] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [500]
[12:40:18] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [500]
[12:40:51] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[12:40:51] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[12:40:51] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[12:40:51] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[12:40:51] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [304]
[12:40:51] - [Guest User] - [::1] - [GET] - [/api-docs/favicon-32x32.png] - [304]
[12:40:58] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [500]
[12:41:46] - [Guest User] - [::1] - [PUT] - [/api/v1/categories] - [201]
[12:48:15] - [Guest User] - [::1] - [GET] - [/api-docs/] - [304]
[12:48:15] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[12:48:15] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[12:48:15] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [200]
[12:48:15] - [Guest User] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[12:48:16] - [Guest User] - [::1] - [GET] - [/api-docs/favicon-32x32.png] - [304]
[12:51:42] - [Guest User] - [::1] - [POST] - [/api/v1/auth/signin] - [401]
[12:51:47] - [Guest User] - [::1] - [POST] - [/api/v1/auth/signin] - [200]
[12:51:56] - [Mohamed Aziz BEN SALEM] - [::1] - [POST] - [/api/v1/categories] - [500]
[12:52:09] - [Mohamed Aziz BEN SALEM] - [::1] - [POST] - [/api/v1/categories] - [500]
[12:54:08] - [Mohamed Aziz BEN SALEM] - [::1] - [POST] - [/api/v1/categories] - [500]
[12:54:54] - [Mohamed Aziz BEN SALEM] - [::1] - [POST] - [/api/v1/categories] - [409]
[12:55:25] - [Mohamed Aziz BEN SALEM] - [::1] - [POST] - [/api/v1/categories] - [200]
[13:02:26] - [Mohamed Aziz BEN SALEM] - [::1] - [POST] - [/api/v1/categories] - [200]
[13:25:51] - [Mohamed Aziz BEN SALEM] - [::1] - [GET] - [/api-docs/] - [304]
[13:25:51] - [Mohamed Aziz BEN SALEM] - [::1] - [GET] - [/api-docs/swagger-ui.css] - [304]
[13:25:51] - [Mohamed Aziz BEN SALEM] - [::1] - [GET] - [/api-docs/swagger-ui-bundle.js] - [304]
[13:25:51] - [Mohamed Aziz BEN SALEM] - [::1] - [GET] - [/api-docs/swagger-ui-standalone-preset.js] - [304]
[13:25:51] - [Mohamed Aziz BEN SALEM] - [::1] - [GET] - [/api-docs/swagger-ui-init.js] - [200]
[13:26:02] - [Mohamed Aziz BEN SALEM] - [::1] - [GET] - [/api/v1/categories?page=1&limit=10] - [200]
